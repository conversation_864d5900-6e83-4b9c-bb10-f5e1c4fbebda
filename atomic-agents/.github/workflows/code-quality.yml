name: Code Quality & Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

permissions:
  contents: read

jobs:
  quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install Poetry
        run: |
          python -m pip install --upgrade pip
          pip install poetry

      - name: Configure Poetry
        run: |
          poetry config virtualenvs.create true
          poetry config virtualenvs.in-project true

      - name: Cache Poetry virtualenv
        uses: actions/cache@v4
        id: cache
        with:
          path: ./.venv
          key: venv-${{ runner.os }}-${{ hashFiles('**/poetry.lock') }}
          restore-keys: |
            venv-${{ runner.os }}-

      - name: Install Dependencies
        run: |
          poetry install --with dev
          poetry run pip list  # Verify installation

      - name: Verify Black Installation
        run: |
          poetry run which black || echo "Black not found"
          poetry run black --version

      - name: Run Black Check
        run: |
          poetry run black --check atomic-agents atomic-assembler atomic-examples atomic-forge
        if: success()

      - name: Run Flake8
        run: poetry run flake8 --extend-exclude=.venv atomic-agents atomic-assembler atomic-examples atomic-forge
        if: success()

      - name: Run Tests
        run: poetry run pytest --cov=atomic_agents atomic-agents
        if: success()
