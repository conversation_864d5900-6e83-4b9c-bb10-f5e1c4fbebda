[tool.poetry]
name = "deep-research"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.11,<4.0"
atomic-agents = {path = "../..", develop = true}
requests = "^2.32.3"
beautifulsoup4 = "^4.12.3"
markdownify = "^0.13.1"
readability-lxml = "^0.8.1"
lxml-html-clean = "^0.4.0"
lxml = "^5.3.0"
python-dotenv = ">=1.0.1,<2.0.0"
openai = ">=1.35.12,<2.0.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
