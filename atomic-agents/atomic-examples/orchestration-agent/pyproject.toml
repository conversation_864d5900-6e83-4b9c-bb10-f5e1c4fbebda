[tool.poetry]
name = "orchestration-agent"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON>et<PERSON>de <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
atomic-agents = {path = "../..", develop = true}
instructor = "^1.6.1"
pydantic = ">=2.10.3,<3.0.0"
sympy = "^1.13.3"
python-dotenv = ">=1.0.1,<2.0.0"
openai = ">=1.35.12,<2.0.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
