[tool.poetry]
name = "web-search-agent"
version = "1.0.0"
description = "Web search agent example for Atomic Agents"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
atomic-agents = {path = "../..", develop = true}
openai = ">=1.35.12,<2.0.0"
pydantic = ">=2.9.2,<3.0.0"
instructor = ">=1.5.2,<2.0.0"
python-dotenv = ">=1.0.1,<2.0.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
