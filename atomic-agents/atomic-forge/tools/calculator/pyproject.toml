[tool.poetry]
name = "calculator"
version = "1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = ">=3.12,<4"
atomic-agents = {path = "../../../", develop = true}
pydantic = ">=2.10.3,<3.0.0"
sympy = ">=1.12,<2"

[tool.poetry.group.dev.dependencies]
coverage = ">=7.6.1,<8.0.0"
pytest-cov = ">=5.0.0,<6.0.0"
pytest = ">=8.3.3,<9.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
