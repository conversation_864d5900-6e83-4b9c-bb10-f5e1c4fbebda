[tool.poetry]
name = "webpage_scraper"
version = "1.0"
description = "Tool for scraping webpage content and converting it to markdown format"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = ">=3.12,<4"
atomic-agents = {path = "../../../", develop = true}
pydantic = ">=2.10.3,<3.0.0"
beautifulsoup4 = ">=4.12.0,<5.0.0"
markdownify = ">=0.11.0,<1.0.0"
readability-lxml = ">=0.8.1,<1.0.0"
requests = ">=2.31.0,<3.0.0"
lxml = ">=5.1.0,<6.0.0"
lxml-html-clean = ">=0.3.1,<1.0.0"

[tool.poetry.group.dev.dependencies]
coverage = ">=7.6.1,<8.0.0"
pytest-cov = ">=5.0.0,<6.0.0"
pytest = ">=8.3.3,<9.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
